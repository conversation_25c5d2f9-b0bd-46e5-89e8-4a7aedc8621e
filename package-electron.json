{"name": "strategy-creator-electron", "version": "2.4.3", "description": "Desktop version of the Strategy Creator", "main": "main.js", "scripts": {"start": "electron .", "dist": "electron-builder", "build-web": "vite build", "prepare-electron": "npm run build-web && npm install"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.xdepredadorxd.strategycreator", "productName": "Strategy Creator 2.4", "directories": {"output": "dist"}, "files": ["dist/**/*", "main.js", "preload.js", "package.json"], "win": {"target": "nsis", "icon": "icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}