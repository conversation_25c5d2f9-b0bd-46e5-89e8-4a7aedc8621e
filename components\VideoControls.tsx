import React, { useRef, useCallback } from 'react';
import { PlayIcon, PauseIcon } from './icons/ToolIcons';

interface VideoControlsProps {
    isPlaying: boolean;
    onPlayPauseClick: () => void;
    currentTime: number;
    duration: number;
    onSeek: (time: number) => void;
    t: (key: string) => string;
}

const formatTime = (timeInSeconds: number) => {
    if (!isFinite(timeInSeconds)) return '0:00';
    const time = Math.round(timeInSeconds);
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

export const VideoControls: React.FC<VideoControlsProps> = ({
    isPlaying,
    onPlayPauseClick,
    currentTime,
    duration,
    onSeek,
    t
}) => {
    const timelineRef = useRef<HTMLDivElement>(null);

    const handleSeek = useCallback((e: React.MouseEvent | MouseEvent) => {
        if (!timelineRef.current || duration === 0) return;

        const rect = timelineRef.current.getBoundingClientRect();
        const clickX = e.clientX - rect.left;
        const width = rect.width;
        const percentage = Math.max(0, Math.min(1, clickX / width));
        
        onSeek(percentage * duration);
    }, [duration, onSeek]);

    const handleMouseDown = (e: React.MouseEvent) => {
        e.preventDefault();
        handleSeek(e);

        const handleMouseMove = (moveEvent: MouseEvent) => {
            handleSeek(moveEvent);
        };
        const handleMouseUp = () => {
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        };

        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);
    };

    if (duration === 0 || !isFinite(duration)) {
        return null; // Don't show controls until video metadata is loaded
    }

    const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

    return (
        <div
            className="absolute bottom-2 left-4 w-1/3 min-w-[280px] max-w-[450px] flex items-center px-2 py-1 space-x-4 z-10"
            style={{ filter: 'drop-shadow(0 3px 6px rgba(0,0,0,0.7))' }}
        >
            <span className="text-white font-mono text-sm select-none">
                {formatTime(currentTime)}
            </span>
            
            <button onClick={onPlayPauseClick} className="text-white p-1 flex-shrink-0">
                <div className="w-5 h-5">
                    {isPlaying ? <PauseIcon /> : <PlayIcon />}
                </div>
            </button>
            
            <div
                ref={timelineRef}
                onMouseDown={handleMouseDown}
                className="relative w-full h-full flex items-center group cursor-pointer py-2"
            >
                {/* Background track */}
                <div className="w-full h-px bg-white/80 rounded-full"></div>
                
                {/* Progress track */}
                <div
                    className="absolute h-px bg-yellow-400 pointer-events-none"
                    style={{ width: `${progressPercentage}%` }}
                ></div>

                {/* Thumb */}
                <div
                    className="absolute top-1/2 w-3 h-3 bg-white rounded-full pointer-events-none transition-transform group-hover:scale-125 [box-shadow:0_0_4px_rgb(0_0_0_/_0.5)]"
                    style={{ 
                        left: `${progressPercentage}%`, 
                        transform: 'translate(-50%, -50%)'
                    }}
                ></div>
            </div>
        </div>
    );
};