
import { UnitType, OtherType } from './types';

export const INITIAL_PALETTE_COLORS = [
    '#FFFFFF', // white
    '#0048FF', // blue
    '#00FF6A', // green
    '#E8FF00', // yellow
    '#A900FF', // purple
    '#FF00D4', // pink
    '#FF0000', // red
];

export const UNIT_TYPES: UnitType[] = [
    UnitType.Sword,
];

export const OTHER_TYPES: { name: string, type: OtherType }[] = [
    { name: 'Mortar', type: OtherType.Mortar },
];

export const FONT_FACES = [
  'Arial',
  'Verdana',
  'Georgia',
  'Times New Roman',
  'Courier New',
  'Impact',
  'Brush Script MT',
  'Comic Sans MS'
];