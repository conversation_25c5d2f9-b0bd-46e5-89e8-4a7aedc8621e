const { app, BrowserWindow, ipc<PERSON>ain, Menu } = require('electron');
const path = require('path');
const { networkInterfaces } = require('os');

// Obtener la dirección MAC
const getMacAddress = () => {
    const nets = networkInterfaces();
    const macAddress = Object.values(nets)
        .flat()
        .find(net => net && !net.internal && net.mac && net.mac !== '00:00:00:00:00:00')?.mac;
    
    console.log('MAC Address detected:', macAddress);
    return macAddress;
};

const macAddress = getMacAddress();

function createWindow() {
    // Crear la ventana del navegador
    const mainWindow = new BrowserWindow({
        width: 1280,
        height: 720,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false
        },
        icon: path.join(__dirname, 'icon.ico'), // Opcional: icono de la aplicación
        show: false // No mostrar hasta que esté listo
    });

    // Cargar la aplicación
    const isDev = process.env.NODE_ENV === 'development';
    
    if (isDev) {
        // En desarrollo, cargar desde el servidor de Vite
        mainWindow.loadURL('http://localhost:5173');
        mainWindow.webContents.openDevTools();
    } else {
        // En producción, cargar desde los archivos compilados
        mainWindow.loadFile(path.join(__dirname, 'dist', 'index.html'));
    }

    // Mostrar la ventana cuando esté lista
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // Manejar el cierre de la ventana
    mainWindow.on('closed', () => {
        app.quit();
    });

    return mainWindow;
}

// Configurar el menú de la aplicación
function createMenu() {
    const template = [
        {
            label: 'Archivo',
            submenu: [
                {
                    label: 'Salir',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Ver',
            submenu: [
                { role: 'reload', label: 'Recargar' },
                { role: 'forceReload', label: 'Forzar Recarga' },
                { role: 'toggleDevTools', label: 'Herramientas de Desarrollador' },
                { type: 'separator' },
                { role: 'resetZoom', label: 'Zoom Normal' },
                { role: 'zoomIn', label: 'Acercar' },
                { role: 'zoomOut', label: 'Alejar' },
                { type: 'separator' },
                { role: 'togglefullscreen', label: 'Pantalla Completa' }
            ]
        },
        {
            label: 'Ayuda',
            submenu: [
                {
                    label: 'Acerca de Strategy Creator',
                    click: () => {
                        // Aquí podrías mostrar un diálogo "Acerca de"
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// Este método se llamará cuando Electron haya terminado la inicialización
app.whenReady().then(() => {
    // Manejar la solicitud de MAC address desde el renderer
    ipcMain.handle('get-mac-address', () => {
        return macAddress;
    });

    createMenu();
    createWindow();

    app.on('activate', () => {
        // En macOS, es común recrear una ventana cuando se hace clic en el icono del dock
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

// Salir cuando todas las ventanas estén cerradas
app.on('window-all-closed', () => {
    // En macOS, es común que las aplicaciones permanezcan activas hasta que el usuario las cierre explícitamente
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Manejar errores
process.on('uncaughtException', (error) => {
    console.error('Error no capturado:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Promesa rechazada no manejada en:', promise, 'razón:', reason);
});
