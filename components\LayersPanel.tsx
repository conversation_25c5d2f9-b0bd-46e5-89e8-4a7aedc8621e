import React, { useState, useMemo, useRef, useEffect } from 'react';
import { type TokenState, type Annotation, UnitType, AnnotationType, OtherType, type LayerEntry, type LayerItemIdentifier, type LayerGroup, type ThemeSettings } from '../types';
import { 
    SwordIcon, TextIcon as UnitTextIcon, 
    MortarIcon
} from './icons/UnitIcons';
import { ArrowIcon, CircleIcon, EyeIcon, EyeSlashIcon, TrashIcon, ChevronRightIcon, LayersIcon, ChevronDownIcon, PlusIcon, SkipToStartIcon, PlayIcon, PauseIcon, BrushIcon } from './icons/ToolIcons';

interface LayersPanelProps {
  layers: LayerEntry[];
  tokens: TokenState[];
  annotations: Annotation[];
  selectedItemIds: LayerItemIdentifier[];
  onSelectItems: (item: LayerItemIdentifier, isShiftClick: boolean, flatVisibleItems: LayerItemIdentifier[]) => void;
  onToggleVisibility: (id: number, type: 'token' | 'annotation') => void;
  onToggleGroupVisibility: (groupId: number) => void;
  onDeleteItem: (id: number, type: 'token' | 'annotation') => void;
  onLayersUpdate: (newLayers: LayerEntry[]) => void;
  getNextId: () => number;
  onResetTokenAnimation: (tokenId: number) => void;
  onToggleTokenAnimation: (tokenId: number) => void;
  onResetGroupAnimation: (groupId: number) => void;
  onToggleGroupAnimation: (groupId: number) => void;
  onAssignItemsToGroup: (targetGroupId: number | null) => void;
  themeSettings: ThemeSettings;
  t: (key: string) => string;
}

const getItemData = (
    identifier: LayerItemIdentifier, 
    tokens: TokenState[], 
    annotations: Annotation[]
): TokenState | Annotation | undefined => {
    if (identifier.type === 'token') {
        return tokens.find(t => t.id === identifier.id);
    }
    return annotations.find(a => a.id === identifier.id);
};

const getItemName = (data: TokenState | Annotation | undefined, t: (key: string) => string): string => {
    if (!data) return '...';
    if (data.type === UnitType.Text && 'text' in data) {
        return data.text || t(`unit_text`);
    }
    if ('name' in data && data.name) return data.name;

    switch (data.type) {
        case UnitType.Sword: return t('unit_sword');
        case UnitType.Other: 
            if ('otherType' in data && data.otherType) return t(`other_${data.otherType.toLowerCase()}`);
            return t('unit_other');
        case UnitType.Custom: return t('unit_custom');
        case UnitType.CustomOther: return t('unit_customother');
        case AnnotationType.Arrow: return t('annotation_arrow');
        case AnnotationType.Circle: return t('annotation_circle');
        case AnnotationType.BrushStroke: return t('annotation_brushstroke');
        default: return 'Item';
    }
};

const itemIcons: Record<string, React.ReactNode> = {
    [UnitType.Sword]: <SwordIcon />,
    [UnitType.Text]: <UnitTextIcon />,
    [OtherType.Mortar]: <MortarIcon />,
    [AnnotationType.Arrow]: <ArrowIcon />,
    [AnnotationType.Circle]: <CircleIcon />,
    [AnnotationType.BrushStroke]: <BrushIcon />,
    default: <LayersIcon />,
};

const getItemIcon = (data: TokenState | Annotation | undefined): React.ReactNode => {
    if (!data) return itemIcons.default;
    if (data.type === UnitType.Custom || data.type === UnitType.CustomOther) {
        if ('customImage' in data && data.customImage) {
            return <img src={data.customImage} alt="custom" className="w-full h-full object-contain" />;
        }
    }
    const key = 'otherType' in data && data.otherType ? data.otherType : data.type;
    return itemIcons[key as string] || itemIcons.default;
};

const flattenLayers = (layers: LayerEntry[]): LayerItemIdentifier[] => {
    const flatList: LayerItemIdentifier[] = [];
    const recurse = (items: LayerEntry[]) => {
        for (const item of items) {
            if (item.type === 'group') {
                if (!item.isCollapsed) {
                    recurse(item.items);
                }
            } else {
                flatList.push(item);
            }
        }
    };
    recurse(layers);
    return flatList;
};


export const LayersPanel: React.FC<LayersPanelProps> = ({
  layers,
  tokens,
  annotations,
  selectedItemIds,
  onSelectItems,
  onToggleVisibility,
  onToggleGroupVisibility,
  onDeleteItem,
  onLayersUpdate,
  getNextId,
  onResetTokenAnimation,
  onToggleTokenAnimation,
  onResetGroupAnimation,
  onToggleGroupAnimation,
  onAssignItemsToGroup,
  themeSettings,
  t,
}) => {
  const [isPanelCollapsed, setIsPanelCollapsed] = useState(true);
  const [editingGroupId, setEditingGroupId] = useState<number | null>(null);
  const [editingGroupName, setEditingGroupName] = useState('');
  const [draggedItem, setDraggedItem] = useState<LayerEntry | null>(null);
  const [dropTarget, setDropTarget] = useState<{ id: number; type: 'group' | 'item'; position: 'before' | 'after' | 'inside' } | null>(null);
  const [isAssigningGroup, setIsAssigningGroup] = useState(false);
  const layerListRef = useRef<HTMLDivElement>(null);
  
  const flatVisibleItems = useMemo(() => flattenLayers(layers), [layers]);

  const allGroups = useMemo(() => {
    const groups: LayerGroup[] = [];
    const findGroups = (entries: LayerEntry[]) => {
        entries.forEach(entry => {
            if (entry.type === 'group') {
                groups.push(entry);
                findGroups(entry.items);
            }
        });
    };
    findGroups(layers);
    return groups;
  }, [layers]);

  const handleCreateGroup = () => {
    const newGroup: LayerGroup = {
      id: getNextId(),
      type: 'group',
      name: t('layers_new_group_default_name'),
      items: [],
      isCollapsed: false,
    };
    onLayersUpdate([newGroup, ...layers]);
  };

  const handleStartEditingGroup = (group: LayerGroup) => {
    setEditingGroupId(group.id);
    setEditingGroupName(group.name);
  };
  
  const handleUpdateGroupName = (groupId: number) => {
    if (!editingGroupName.trim()) return;

    const updateRec = (entries: LayerEntry[]): LayerEntry[] => {
        return entries.map(entry => {
            if (entry.type === 'group') {
                if (entry.id === groupId) {
                    return { ...entry, name: editingGroupName };
                }
                return { ...entry, items: updateRec(entry.items) };
            }
            return entry;
        });
    };
    onLayersUpdate(updateRec(layers));
    setEditingGroupId(null);
  };

  const handleToggleCollapse = (groupId: number) => {
    const toggleRec = (entries: LayerEntry[]): LayerEntry[] => {
        return entries.map(entry => {
            if (entry.type === 'group') {
                if (entry.id === groupId) {
                    return { ...entry, isCollapsed: !entry.isCollapsed };
                }
                return { ...entry, items: toggleRec(entry.items) };
            }
            return entry;
        });
    };
    onLayersUpdate(toggleRec(layers));
  };
  
  const handleDragStart = (e: React.DragEvent, item: LayerEntry) => {
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', JSON.stringify(item));
    setDraggedItem(item);
  };

  const handleDragOver = (e: React.DragEvent, targetItem: LayerEntry) => {
      e.preventDefault();
      if (!draggedItem) return;

      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      const isOverTopHalf = e.clientY < rect.top + rect.height / 2;
      
      if (targetItem.type === 'group') {
        const isOverGroupHeader = e.clientY < rect.top + 32; // Approx header height
        if (isOverGroupHeader) {
            setDropTarget({ id: targetItem.id, type: 'group', position: isOverTopHalf ? 'before' : 'inside' });
        } else {
             setDropTarget({ id: targetItem.id, type: 'group', position: 'inside' });
        }
      } else {
          setDropTarget({ id: targetItem.id, type: 'item', position: isOverTopHalf ? 'before' : 'after' });
      }
  };
  
  const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      if (!draggedItem || !dropTarget) {
          setDraggedItem(null);
          setDropTarget(null);
          return;
      };

      if (draggedItem.type === 'group' && dropTarget.type === 'item') {
         const isDescendant = (parent: LayerGroup, childId: number): boolean => {
             return parent.items.some(item => 
                 (item.type === 'group' && (item.id === childId || isDescendant(item, childId)))
             );
         };
         if (isDescendant(draggedItem, dropTarget.id)) {
            setDraggedItem(null);
            setDropTarget(null);
            return;
         }
      }
      
      const layersCopy = JSON.parse(JSON.stringify(layers));

      // 1. Remove the item from its original position
      let foundItem: LayerEntry | undefined;
      const removeItem = (entries: LayerEntry[], idToRemove: number, typeToRemove: 'group' | 'token' | 'annotation'): LayerEntry[] => {
          return entries.filter(entry => {
              if (entry.id === idToRemove && entry.type === typeToRemove) {
                  foundItem = entry;
                  return false;
              }
              if (entry.type === 'group') {
                  entry.items = removeItem(entry.items, idToRemove, typeToRemove);
              }
              return true;
          });
      };
      
      const newLayers = removeItem(layersCopy, draggedItem.id, draggedItem.type);
      if (!foundItem) return; // Should not happen
      
      // 2. Insert the item at the new position
      let inserted = false;
      const insertItem = (entries: LayerEntry[]): LayerEntry[] => {
          if (inserted) return entries;
          const result: LayerEntry[] = [];
          for (const entry of entries) {
              if (entry.id === dropTarget.id) {
                  if (entry.type === 'group' && dropTarget.position === 'inside') {
                       result.push({ ...entry, items: [foundItem!, ...entry.items] });
                       inserted = true;
                  } else if (dropTarget.position === 'before') {
                      result.push(foundItem!, entry);
                      inserted = true;
                  } else { // 'after'
                      result.push(entry, foundItem!);
                      inserted = true;
                  }
              } else {
                  if (entry.type === 'group') {
                      result.push({ ...entry, items: insertItem(entry.items) });
                  } else {
                      result.push(entry);
                  }
              }
          }
          return result;
      };

      const finalLayers = insertItem(newLayers);
      if(!inserted && dropTarget === null) {
          finalLayers.push(foundItem);
      }
      
      onLayersUpdate(finalLayers);
      setDraggedItem(null);
      setDropTarget(null);
  };
  
  const handleDragEnd = () => {
    setDraggedItem(null);
    setDropTarget(null);
  };

  const isGroupAnimatable = (group: LayerGroup): boolean => {
      const checkItems = (items: LayerEntry[]): boolean => {
          return items.some(item => {
              if (item.type === 'token') {
                  const token = tokens.find(t => t.id === item.id);
                  return !!token && token.path.length > 0;
              }
              if (item.type === 'group') return checkItems(item.items);
              return false;
          });
      };
      return checkItems(group.items);
  };

  const isGroupAnimating = (group: LayerGroup): boolean => {
      const checkItems = (items: LayerEntry[]): boolean => {
          return items.some(item => {
              if (item.type === 'token') {
                  const token = tokens.find(t => t.id === item.id);
                  return !!token && (token.isAnimating ?? true) && token.path.length > 0;
              }
              if (item.type === 'group') return checkItems(item.items);
              return false;
          });
      };
      return checkItems(group.items);
  };

  const renderLayerEntry = (entry: LayerEntry, depth: number) => {
    const isSelected = selectedItemIds.some(i => i.id === entry.id && i.type === entry.type);
    
    if (entry.type === 'group') {
      const isAnimatable = isGroupAnimatable(entry);
      const isAnimating = isGroupAnimating(entry);

      return (
        <div key={entry.id}>
          <div
            className={`flex items-center rounded-md transition-colors duration-150 ${ isSelected ? 'bg-blue-600/30' : 'hover:bg-white/10' }`}
            style={{ paddingLeft: `${depth * 12}px` }}
            draggable
            onDragStart={e => handleDragStart(e, entry)}
            onDragOver={e => handleDragOver(e, entry)}
            onDragEnd={handleDragEnd}
          >
            <button onClick={() => handleToggleCollapse(entry.id)} className="p-1 opacity-70 hover:opacity-100 flex-shrink-0">
                <div className="w-4 h-4 transition-transform duration-200" style={{ transform: entry.isCollapsed ? 'rotate(-90deg)' : 'rotate(0deg)' }}>
                    <ChevronDownIcon />
                </div>
            </button>
            <div className="w-4 h-4 mx-1 text-yellow-400 flex-shrink-0"><LayersIcon /></div>
            
            {editingGroupId === entry.id ? (
                <input
                    type="text"
                    value={editingGroupName}
                    onChange={e => setEditingGroupName(e.target.value)}
                    onBlur={() => handleUpdateGroupName(entry.id)}
                    onKeyDown={e => e.key === 'Enter' && handleUpdateGroupName(entry.id)}
                    className="flex-grow bg-black/30 p-0.5 rounded-sm outline-none ring-1 ring-blue-500"
                    autoFocus
                />
            ) : (
                <span onDoubleClick={() => handleStartEditingGroup(entry)} className="flex-grow text-sm truncate px-1 cursor-pointer">{entry.name}</span>
            )}
            
            <div className="flex items-center mr-1 space-x-1 flex-shrink-0">
               {isAnimatable && (
                 <>
                    <button onClick={() => onToggleGroupAnimation(entry.id)} title={t('layers_pause_group_animation')} className="p-0.5 opacity-70 hover:opacity-100"><div className="w-3.5 h-3.5">{isAnimating ? <PauseIcon /> : <PlayIcon />}</div></button>
                    <button onClick={() => onResetGroupAnimation(entry.id)} title={t('layers_reset_group_position')} className="p-0.5 opacity-70 hover:opacity-100"><div className="w-3.5 h-3.5"><SkipToStartIcon /></div></button>
                 </>
               )}
               <button onClick={() => onToggleGroupVisibility(entry.id)} title={t('layers_toggle_visibility')} className="p-0.5 opacity-70 hover:opacity-100"><div className="w-4 h-4"><EyeIcon /></div></button>
            </div>
          </div>
          {!entry.isCollapsed && entry.items.map(item => renderLayerEntry(item, depth + 1))}
        </div>
      );
    }
    
    // It's a token or annotation
    const itemData = getItemData(entry, tokens, annotations);
    if (!itemData) return null;
    
    const isAnimatable = entry.type === 'token' && (itemData as TokenState).path.length > 0;
    const isAnimating = isAnimatable && ((itemData as TokenState).isAnimating ?? true);
    
    return (
      <div
        key={`${entry.type}-${entry.id}`}
        className={`flex items-center rounded-md transition-colors duration-150 relative ${isSelected ? 'bg-blue-600/30' : 'hover:bg-white/10'}`}
        style={{ paddingLeft: `${depth * 12}px` }}
        onClick={(e) => onSelectItems(entry, e.shiftKey, flatVisibleItems)}
        draggable
        onDragStart={e => handleDragStart(e, entry)}
        onDragOver={e => handleDragOver(e, entry)}
        onDragEnd={handleDragEnd}
      >
        <div className="w-4 h-4 mx-2 flex-shrink-0">{getItemIcon(itemData)}</div>
        <span className="flex-grow text-sm truncate pr-1">{getItemName(itemData, t)}</span>
        <div className="flex items-center mr-1 space-x-1 flex-shrink-0">
            {isAnimatable && (
                <>
                    <button onClick={(e) => { e.stopPropagation(); onToggleTokenAnimation(entry.id); }} title={isAnimating ? t('layers_pause_animation') : t('layers_play_animation')} className="p-0.5 opacity-70 hover:opacity-100"><div className="w-3.5 h-3.5">{isAnimating ? <PauseIcon /> : <PlayIcon />}</div></button>
                    <button onClick={(e) => { e.stopPropagation(); onResetTokenAnimation(entry.id); }} title={t('layers_reset_position')} className="p-0.5 opacity-70 hover:opacity-100"><div className="w-3.5 h-3.5"><SkipToStartIcon /></div></button>
                </>
            )}
            <button onClick={(e) => { e.stopPropagation(); onToggleVisibility(entry.id, entry.type); }} title={t('layers_toggle_visibility')} className="p-0.5 opacity-70 hover:opacity-100"><div className="w-4 h-4">{itemData.isVisible !== false ? <EyeIcon /> : <EyeSlashIcon />}</div></button>
            <button onClick={(e) => { e.stopPropagation(); onDeleteItem(entry.id, entry.type); }} title={t('layers_delete_item')} className="p-0.5 opacity-70 hover:text-red-500 hover:opacity-100"><div className="w-4 h-4"><TrashIcon /></div></button>
        </div>
        {dropTarget && dropTarget.id === entry.id && dropTarget.type === 'item' && (
            <div className={`absolute left-0 w-full h-0.5 pointer-events-none ${dropTarget.position === 'before' ? 'top-0' : 'bottom-0'}`}>
                <div className="h-full bg-blue-400 animate-pulse"></div>
            </div>
        )}
      </div>
    );
  };
  
  const panelStyle: React.CSSProperties = {
    background: `linear-gradient(${themeSettings.gradientAngle}deg, ${themeSettings.gradientColors.map(c => `${c}99`).join(', ')})`,
    color: themeSettings.textColor,
    width: `${16 * themeSettings.uiScale}rem`,
  };

  if (isPanelCollapsed) {
    const buttonStyle: React.CSSProperties = {
      background: `linear-gradient(${themeSettings.gradientAngle}deg, ${themeSettings.gradientColors.join(', ')})`,
      color: themeSettings.textColor,
      textShadow: '0 1px 2px rgba(0,0,0,0.4)',
    };
    return (
        <button 
            onClick={() => setIsPanelCollapsed(false)} 
            className="fixed top-4 right-4 z-20 w-14 h-14 p-3 rounded-lg transition-all duration-200 shadow-lg border flex items-center justify-center border-t-white/20 border-l-white/20 border-b-black/40 border-r-black/40 hover:brightness-110 active:brightness-90 active:shadow-inner active:border-t-black/40 active:border-l-black/40 active:border-b-white/20 active:border-r-white/20"
            title={t('layers_open')}
            style={buttonStyle}
        >
            <div className="w-8 h-8 text-blue-400"><LayersIcon /></div>
        </button>
    );
  }

  return (
    <div 
        className="fixed top-0 right-0 h-full flex flex-col p-2 shadow-2xl z-20 backdrop-blur-md"
        style={panelStyle}
    >
      <div className="flex items-center justify-between mb-2 flex-shrink-0">
        <h2 className="text-lg font-bold text-blue-400 flex items-center">
            <div className="w-6 h-6 mr-2 flex-shrink-0"><LayersIcon /></div>
            {t('layers_title')}
        </h2>
        <div className="flex items-center gap-1">
            <button onClick={handleCreateGroup} title={t('layers_create_group')} className="w-7 h-7 flex items-center justify-center bg-black/20 hover:bg-black/30 rounded-md opacity-70 hover:opacity-100 transition-colors">
                <div className="w-4 h-4"><PlusIcon /></div>
            </button>
            <button onClick={() => setIsPanelCollapsed(true)} title={t('layers_collapse')} className="w-7 h-7 flex items-center justify-center rounded-md hover:bg-white/10 opacity-70 hover:opacity-100 transition-colors">
                <div className="w-5 h-5"><ChevronRightIcon /></div>
            </button>
        </div>
      </div>
      
      {selectedItemIds.length > 0 && (
          <div className="flex-shrink-0 mb-2 pb-2 border-b border-white/20 relative">
              <button onClick={() => setIsAssigningGroup(p => !p)} className="w-full bg-black/20 hover:bg-black/40 p-2 rounded-md text-sm flex items-center justify-between">
                <span>{t('layers_assign_to_group')}</span>
                <div className="w-4 h-4"><ChevronDownIcon /></div>
              </button>
              {isAssigningGroup && (
                  <div className="absolute top-full mt-1 w-full bg-gray-900 border border-gray-600 rounded-md shadow-lg z-30 max-h-48 overflow-y-auto custom-scrollbar">
                      <div className="text-sm hover:bg-gray-700 p-2 cursor-pointer" onClick={() => { onAssignItemsToGroup(null); setIsAssigningGroup(false); }}>{t('layers_group_none')}</div>
                      {allGroups.map(group => (
                          <div key={group.id} className="text-sm hover:bg-gray-700 p-2 cursor-pointer truncate" onClick={() => { onAssignItemsToGroup(group.id); setIsAssigningGroup(false); }}>{group.name}</div>
                      ))}
                  </div>
              )}
          </div>
      )}

      <div 
        ref={layerListRef}
        className="flex-grow overflow-y-auto space-y-1 custom-scrollbar pr-1"
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
      >
        {layers.length > 0 ? layers.map(entry => renderLayerEntry(entry, 0)) : <p className="opacity-50 text-center text-sm p-4">{t('layers_no_items')}</p>}
      </div>
    </div>
  );
};