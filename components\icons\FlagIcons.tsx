import React from 'react';

export const UsaFlagIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 600" className="w-full h-full">
        <path fill="#fff" d="M0 0h900v600H0z" />
        <path fill="#b22234" d="M0 0h900v50H0zm0 100h900v50H0zm0 100h900v50H0zm0 100h900v50H0zm0 100h900v50H0zm0 100h900v50H0z" />
        <path fill="#3c3b6e" d="M0 0h400v350H0z" />
        <g fill="#fff">
            {[...Array(50)].map((_, i) => {
                const row = Math.floor(i / (i < 25 ? 5 : 4));
                const col = i % (i < 25 ? 5 : 4);
                const x = col * 60 + 30 + (row % 2) * 30;
                const y = row * 50 + 40;
                return (
                    <g key={i} transform={`translate(${x},${y})`}>
                        <g transform="translate(0 -17.5)">
                            <path d="M0-12.5L2.9-4 9.5-4 4.7 1.2 7.3 8.5 0 4 -7.3 8.5-4.7 1.2-9.5-4-2.9-4z" />
                        </g>
                    </g>
                );
            })}
        </g>
    </svg>
);

export const PeruFlagIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 600" className="w-full h-full">
        <path fill="#c8102e" d="M0 0h300v600H0z" />
        <path fill="#fff" d="M300 0h300v600H300z" />
        <path fill="#c8102e" d="M600 0h300v600H600z" />
    </svg>
);
