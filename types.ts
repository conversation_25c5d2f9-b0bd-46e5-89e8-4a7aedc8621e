
export enum UnitType {
  Sword = 'Sword',
  Text = 'Text',
  Other = 'Other',
  Custom = 'Custom',
  CustomOther = 'CustomOther',
}

export enum OtherType {
  Mortar = 'Mortar',
}

export interface Point {
  x: number;
  y: number;
}

export interface TokenState {
  id: number;
  type: UnitType;
  color: string;
  number: number | null;
  position: Point;
  path: Point[];
  animationProgress: number; // 0 to 1
  patrolForward?: boolean;
  size?: number; // scale factor
  text?: string;
  fontFamily?: string;
  outlineColor1?: string;
  outlineColor2?: string;
  outlineWidth?: number;
  otherType?: OtherType;
  rotation?: number; // in degrees
  isVisible?: boolean;
  animationSpeed?: number;
  isPatrol?: boolean;
  isFlipped?: boolean;
  isAnimating?: boolean;
  customImage?: string;
  name?: string;
  isNeon?: boolean;
  glowColor?: string;
  glowSize?: number;
}

export enum AnnotationType {
  Arrow = 'Arrow',
  Circle = 'Circle',
  BrushStroke = 'BrushStroke',
}

export interface BrushSettings {
  color: string;
  size: number;
  opacity: number;
  effect: 'none' | 'rainbow-cycle' | 'flashing' | 'breathing';
  glowColor: string;
  glowSize: number;
  isNeon: boolean;
  smoothing: number;
}

export interface ArrowSettings {
  color: string;
  strokeWidthStart: number;
  strokeWidthEnd: number;
  arrowheadLength: number;
  arrowheadWidth: number;
  isAnimated: boolean;
  animatingCircleRadius: number;
  animatingCircleColor: string;
  animationDuration: number;
  isNeon?: boolean;
  glowColor?: string;
  glowSize?: number;
}

export interface ArrowAnnotation {
  type: AnnotationType.Arrow;
  id: number;
  start: Point;
  end: Point;
  control: Point;
  color: string;
  isVisible?: boolean;
  strokeWidthStart?: number;
  strokeWidthEnd?: number;
  arrowheadLength?: number;
  arrowheadWidth?: number;
  isAnimated?: boolean;
  animatingCircleRadius?: number;
  animatingCircleColor?: string;
  animationDuration?: number;
  isNeon?: boolean;
  glowColor?: string;
  glowSize?: number;
}

export interface CircleAnnotation {
  type: AnnotationType.Circle;
  id: number;
  center: Point;
  radius: number;
  color: string;
  isVisible?: boolean;
}

export interface BrushPathSegment {
    path: Point[];
    settings: BrushSettings;
}

export interface BrushStrokeAnnotation {
  type: AnnotationType.BrushStroke;
  id: number;
  segments: BrushPathSegment[];
  isVisible?: boolean;
}

export type Annotation = ArrowAnnotation | CircleAnnotation | BrushStrokeAnnotation;

export interface CustomUnit {
  id: number;
  name: string;
  imageData: string;
}

export interface TextPreset {
  id: number;
  content: string;
  size: number;
  fontFamily: string;
}

export type Tool = 'select' | 'move' | 'clone' | 'path' | 'arrow' | 'circle' | 'eraser' | 'text' | 'zoom' | 'enlarge' | 'brush';

export type DrawingState =
  | { type: 'path'; pathForTokenId: number; points: Point[] }
  | { type: 'circle'; start: Point; current: Point }
  | { type: 'arrow'; stage: 'defining-end'; start: Point; current: Point }
  | { type: 'arrow'; stage: 'defining-control'; start: Point; end: Point; current: Point }
  | { type: 'moving'; itemType: 'token' | 'annotation'; itemId: number; offset: Point; current: Point }
  | { type: 'cloning'; tokenToClone: TokenState; current: Point }
  | { type: 'zoom'; start: Point; current: Point }
  | { type: 'enlarging'; tokenId: number; startY: number; startSize: number; current: Point }
  | { type: 'brush'; segments: BrushPathSegment[]; lazyPoint: Point };

export interface ViewTransform {
  scale: number;
  translateX: number;
  translateY: number;
}

export type LayerItemIdentifier = {
  id: number;
  type: 'token' | 'annotation';
};

export interface LayerGroup {
  id: number;
  type: 'group';
  name: string;
  items: LayerEntry[];
  isCollapsed: boolean;
}

export type LayerEntry = LayerItemIdentifier | LayerGroup;

export interface HistoryState {
    tokens: TokenState[];
    annotations: Annotation[];
    layers: LayerEntry[];
    customUnits: CustomUnit[];
    customOthers: CustomUnit[];
}
  
export interface ProjectData {
  mapImage: string | null;
  mapVideo?: string | null;
  mapDimensions: { width: number; height: number; } | null;
  tokens: TokenState[];
  annotations: Annotation[];
  paletteColors: string[];
  nextId: number;
  customUnits?: CustomUnit[];
  customOthers?: CustomUnit[];
  layers?: LayerEntry[];
}

export interface ThemeSettings {
  gradientColors: [string, string, string];
  gradientAngle: number;
  textColor: string;
  uiScale: number;
  buttonColor: string;
}