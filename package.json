{"name": "strategy-creator-2.4", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "electron . --dev", "dist": "npm run build && electron-builder"}, "main": "main.js", "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "electron": "^28.3.3", "electron-builder": "^24.13.3", "typescript": "~5.7.2", "vite": "^6.2.0"}, "build": {"appId": "com.xdepredadorxd.strategycreator", "productName": "Strategy Creator 2.4", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "main.js", "preload.js", "package.json"], "win": {"target": "portable"}}}