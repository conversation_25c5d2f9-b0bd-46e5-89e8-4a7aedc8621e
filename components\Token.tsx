import React from 'react';
import { type TokenState, UnitType, OtherType, type Point } from '../types';
import { 
    SwordIcon,
    MortarIcon
} from './icons/UnitIcons';

interface TokenProps {
  token: TokenState;
  mapToScreen: (point: Point) => Point;
  mapScale: number;
  globalTokenSize: number;
  viewScale: number;
}

const unitIcons: Record<string, React.ReactNode> = {
  [UnitType.Sword]: <SwordIcon />,
};

const otherIcons: Record<OtherType, React.ReactNode> = {
  [OtherType.Mortar]: <MortarIcon />,
};

const BASE_TOKEN_SIZE_ON_MAP = 35; // Base size in original map pixels
const BASE_FONT_SIZE = 1; // Base rem size

export const Token: React.FC<TokenProps> = ({ token, mapToScreen, mapScale, globalTokenSize, viewScale }) => {
  const { 
    id, position, type, color, number, size = 1, text, fontFamily, 
    otherType, rotation = 0, customImage, outlineColor1, 
    outlineColor2, outlineWidth = 0, isPatrol, patrolForward, isFlipped,
    isNeon, glowColor, glowSize = 0
  } = token;

  const screenPosition = mapToScreen(position);
  const globalScale = 1 + (globalTokenSize / 100);
  const finalSizeMultiplier = size * globalScale;

  if (type === UnitType.Text) {
    const renderedFontSize = finalSizeMultiplier * BASE_FONT_SIZE * 16 * mapScale; 
    const finalGlowSize = glowSize * finalSizeMultiplier * mapScale;

    return (
      <div
        className="absolute transition-transform duration-100 ease-linear pointer-events-none"
        style={{
          left: `${screenPosition.x}px`,
          top: `${screenPosition.y}px`,
          transform: `translate(-50%, -50%) rotate(${rotation}deg)`,
          pointerEvents: 'none',
        }}
      >
        <span
          className="font-bold whitespace-nowrap text-outline-anim"
          style={{
            fontSize: `${renderedFontSize}px`,
            fontFamily: fontFamily,
            color: color,
            textShadow: (isNeon && glowSize > 0) ? `0 0 ${finalGlowSize}px ${glowColor}` : undefined,
            '--outline-color-1': outlineColor1 || '#FFFFFF',
            '--outline-color-2': outlineColor2 || '#000080',
            '--outline-width': `${outlineWidth * finalSizeMultiplier * mapScale}px`,
            display: 'inline-block',
          } as React.CSSProperties}
        >
          {text}
        </span>
      </div>
    );
  }

  const isCustom = type === UnitType.Custom || type === UnitType.CustomOther;
  const filterId = `outline-${id}`;
  
  const width = BASE_TOKEN_SIZE_ON_MAP * finalSizeMultiplier * mapScale;
  const height = BASE_TOKEN_SIZE_ON_MAP * finalSizeMultiplier * mapScale;
  
  let iconContent: React.ReactNode = null;
  if (type === UnitType.Other && otherType) {
      iconContent = otherIcons[otherType];
  } else if (unitIcons[type]) {
      iconContent = unitIcons[type];
  }
  
  const color1 = outlineColor1 || '#FFFFFF';
  const color2 = outlineColor2 || '#000080';
  const isAnimated = outlineColor2 && color1 !== color2;

  // A simplified, more robust formula for the outline radius.
  // This value is in the SVG's viewBox coordinate system.
  // The final visual thickness scales correctly because the entire SVG is scaled.
  const radius = outlineWidth * 1.5;
    
  const patrolFlip = isPatrol && !(patrolForward ?? true);
  const shouldFlip = (isFlipped ?? false) !== patrolFlip;

  return (
    <div
      className="absolute transition-transform duration-100 ease-linear pointer-events-none"
      style={{
        width: `${width}px`,
        height: `${height}px`,
        left: `${screenPosition.x}px`,
        top: `${screenPosition.y}px`,
        transform: `translate(-50%, -50%) rotate(${rotation}deg)`,
      }}
    >
      <svg 
        viewBox="0 0 100 100" 
        className="w-full h-full"
        style={{ filter: outlineWidth > 0 ? `url(#${filterId})` : 'none', willChange: 'filter', overflow: 'visible' }}
      >
        <defs>
          <filter id={filterId} x="-50%" y="-50%" width="200%" height="200%">
              <feMorphology in="SourceAlpha" result="DILATED" operator="dilate" radius={radius} />
              <feFlood floodColor={color1} result="flood">
                {isAnimated && (
                    <animate 
                        attributeName="flood-color"
                        values={`${color1};${color2};${color1}`}
                        dur="2s"
                        repeatCount="indefinite"
                    />
                )}
              </feFlood>
              <feComposite in="flood" in2="DILATED" operator="in" result="OUTLINE" />
              <feMerge>
                  <feMergeNode in="OUTLINE" />
                  <feMergeNode in="SourceGraphic" />
              </feMerge>
          </filter>
        </defs>

        <g transform={shouldFlip ? 'scale(-1, 1) translate(-100, 0)' : 'none'}>
            {isCustom && customImage ? (
              // Add a margin to custom images to prevent the outline filter from creating a square artifact.
              <image href={customImage} x="10" y="10" height="80" width="80" />
            ) : (
              <g transform="translate(15, 15) scale(0.7, 0.7)" style={{color: color}} className="token-icon">
                {iconContent}
              </g>
            )}
        </g>

        {number !== null && (
          <text
            x="50"
            y="55"
            textAnchor="middle"
            dominantBaseline="middle"
            stroke="black"
            strokeWidth="3"
            paintOrder="stroke"
            fontSize="40"
            fontWeight="bold"
            fontFamily="sans-serif"
            className="number-color-anim"
          >
            {number}
          </text>
        )}
      </svg>
    </div>
  );
};