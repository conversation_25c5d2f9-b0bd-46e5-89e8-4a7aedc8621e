
import React from 'react';

const IconWrapper: React.FC<{ children: React.ReactNode; viewBox?: string }> = ({ children, viewBox = "0 0 24 24" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox={viewBox} fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
        {children}
    </svg>
);

export const SwordIcon: React.FC = () => (
    <IconWrapper viewBox="0 0 100 100">
        <g transform="rotate(45 50 50)">
            <line x1="50" y1="20" x2="50" y2="80" strokeWidth="8" />
            <line x1="35" y1="65" x2="65" y2="65" strokeWidth="8" />
        </g>
        <g transform="rotate(-45 50 50)">
            <line x1="50" y1="20" x2="50" y2="80" strokeWidth="8" />
            <line x1="35" y1="65" x2="65" y2="65" strokeWidth="8" />
        </g>
    </IconWrapper>
);

export const TextIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-full h-full">
        <path fillRule="evenodd" d="M5.25 3a.75.75 0 0 1 .75.75v1.5h8.5V3.75a.75.75 0 0 1 1.5 0v1.5H16.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75-.75H3.75a.75.75 0 0 1-.75-.75V6a.75.75 0 0 1 .75-.75h.5V3.75A.75.75 0 0 1 5.25 3ZM12 15a.75.75 0 0 1-.75-.75v-1.5a.75.75 0 0 1 1.5 0v1.5a.75.75 0 0 1-.75-.75ZM12 6a.75.75 0 0 1 .75.75v3a.75.75 0 0 1-1.5 0v-3A.75.75 0 0 1 12 6Z" clipRule="evenodd" />
    </svg>
);

const GeoIconWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" className="w-full h-full">
        {children}
    </svg>
);

export const MortarIcon: React.FC = () => (
    <GeoIconWrapper>
        <circle cx="50" cy="50" r="25" fill="currentColor" />
    </GeoIconWrapper>
);
