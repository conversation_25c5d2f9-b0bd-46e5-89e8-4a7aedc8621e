
import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { type TokenState, type Annotation, type Point, UnitType, AnnotationType, type DrawingState, type Tool, type ViewTransform, type ArrowSettings, type BrushSettings, BrushStrokeAnnotation, BrushPathSegment } from '../types';
import { Token } from './Token';
import { VideoControls } from './VideoControls';
import { XIcon } from './icons/ToolIcons';

interface CanvasProps {
  mapImage: string | null;
  mapVideo: string | null;
  mapDimensions: { width: number; height: number } | null;
  tokens: TokenState[];
  annotations: Annotation[];
  currentBrushAnnotation: BrushStrokeAnnotation | null;
  onDrop: (data: string, position: Point) => void;
  onMouseDown: (mapPoint: Point, screenPoint: Point) => void;
  onMouseMove: (mapPoint: Point, screenPoint: Point) => void;
  onMouseUp: (canvasRect: DOMRect | undefined) => void;
  onWheel: (e: React.WheelEvent, point: Point) => void;
  drawingState: DrawingState | null;
  activeTool: Tool;
  selectedColor: string;
  arrowSettings: ArrowSettings;
  brushSettings: BrushSettings;
  editingTokenId: number | null;
  onTokenUpdate: (tokenId: number, updates: Partial<TokenState>) => void;
  onFinishEditing: () => void;
  viewTransform: ViewTransform | null;
  onZoomReset: () => void;
  globalTokenSize: number;
  arePathsVisible: boolean;
  mapRotation: number;
  isMapRotating: boolean;
  videoRef: React.RefObject<HTMLVideoElement>;
  isVideoPlaying: boolean;
  onVideoPlayPause: (playing: boolean) => void;
  videoCurrentTime: number;
  onVideoTimeUpdate: (time: number) => void;
  videoDuration: number;
  onVideoDurationChange: (duration: number) => void;
  t: (key: string) => string;
}

const hexToRgba = (hex: string, alpha: number): string => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

const pointsToPath = (points: Point[]): string => {
    if (!points || points.length === 0) return '';
    if (points.length === 1) return `M ${points[0].x} ${points[0].y} L ${points[0].x} ${points[0].y}`;
    
    let pathStr = `M ${points[0].x} ${points[0].y}`;
    for (let i = 1; i < points.length; i++) {
        pathStr += ` L ${points[i].x} ${points[i].y}`;
    }
    return pathStr;
};


const SvgOverlay: React.FC<{ 
    tokens: TokenState[];
    annotations: Annotation[], 
    currentBrushAnnotation: BrushStrokeAnnotation | null,
    drawingState: DrawingState | null, 
    paths: {path: Point[], isVisible?: boolean}[], 
    selectedColor: string, 
    mapToScreen: (point: Point) => Point,
    mapScale: number,
    arrowSettings: ArrowSettings,
    brushSettings: BrushSettings,
    arePathsVisible: boolean,
}> = ({ tokens, annotations, currentBrushAnnotation, drawingState, paths, selectedColor, mapToScreen, mapScale, arrowSettings, brushSettings, arePathsVisible }) => {
    
    const renderPath = (path: Point[], color: string, strokeDash: string = '5,5') => {
        if (path.length < 2) return null;
        const screenPath = path.map(mapToScreen);
        return <polyline points={screenPath.map(pt => `${pt.x},${pt.y}`).join(' ')} fill="none" stroke={color} strokeWidth={Math.max(0.5, 2 * mapScale)} strokeDasharray={strokeDash} />
    };

    const getArrowheadPoints = (endPoint: Point, controlPoint: Point, length: number, width: number): string => {
        const dirX = endPoint.x - controlPoint.x;
        const dirY = endPoint.y - controlPoint.y;
        const len = Math.hypot(dirX, dirY);
        if (len === 0) return "";
        
        const normX = dirX / len;
        const normY = dirY / len;
        
        const arrowLength = length;
        const arrowWidth = width;
    
        const tip = endPoint;
        const baseCenterX = tip.x - normX * arrowLength;
        const baseCenterY = tip.y - normY * arrowLength;
        const perpX = -normY;
        const perpY = normX;
        const p1x = baseCenterX + perpX * (arrowWidth / 2);
        const p1y = baseCenterY + perpY * (arrowWidth / 2);
        const p2x = baseCenterX - perpX * (arrowWidth / 2);
        const p2y = baseCenterY - perpY * (arrowWidth / 2);
        return `${tip.x},${tip.y} ${p1x},${p1y} ${p2x},${p2y}`;
    };

    const findIntersectionT = (P0: Point, P1: Point, P2: Point, arrowheadLength: number): number => {
        const dirX = P2.x - P1.x;
        const dirY = P2.y - P1.y;
        const len = Math.hypot(dirX, dirY);
        if (len < 1e-6) return 1.0; 
    
        const normDirX = dirX / len;
        const normDirY = dirY / len;
    
        const baseCenterX = P2.x - normDirX * arrowheadLength;
        const baseCenterY = P2.y - normDirY * arrowheadLength;
    
        const Ax = P0.x - 2 * P1.x + P2.x;
        const Bx = 2 * (P1.x - P0.x);
        const Cx = P0.x;
        const Ay = P0.y - 2 * P1.y + P2.y;
        const By = 2 * (P1.y - P0.y);
        const Cy = P0.y;
    
        const A = normDirX * Ax + normDirY * Ay;
        const B = normDirX * Bx + normDirY * By;
        const C = normDirX * (Cx - baseCenterX) + normDirY * (Cy - baseCenterY);
        
        if (Math.abs(A) < 1e-6) {
            if (Math.abs(B) < 1e-6) return 1.0;
            const t = -C / B;
            return (t >= 0 && t <= 1) ? t : 1.0;
        }
    
        const discriminant = B * B - 4 * A * C;
        if (discriminant < 0) return 1.0;
    
        const sqrtDiscriminant = Math.sqrt(discriminant);
        const t1 = (-B + sqrtDiscriminant) / (2 * A);
        const t2 = (-B - sqrtDiscriminant) / (2 * A);
    
        const t1Valid = t1 >= 0 && t1 <= 1;
        const t2Valid = t2 >= 0 && t2 <= 1;
    
        if (t1Valid && t2Valid) return Math.max(t1, t2);
        if (t1Valid) return t1;
        if (t2Valid) return t2;
        
        return 1.0;
    };
    
    const getTaperedArrowBodyPath = (start: Point, control: Point, end: Point, widthStart: number, widthEnd: number, t_end: number): string => {
        if (t_end <= 0) return "";
        const segments = 20;
        const points1: Point[] = [];
        const points2: Point[] = [];
    
        const curve = (t: number, p0: number, p1: number, p2: number) => (1 - t) ** 2 * p0 + 2 * (1 - t) * t * p1 + t ** 2 * p2;
        const derivative = (t: number, p0: number, p1: number, p2: number) => 2 * (1 - t) * (p1 - p0) + 2 * t * (p2 - p1);
    
        for (let i = 0; i <= segments; i++) {
            const t = (i / segments) * t_end;
            if (t > t_end) continue;
    
            const px = curve(t, start.x, control.x, end.x);
            const py = curve(t, start.y, control.y, end.y);
            
            let dx = derivative(t, start.x, control.x, end.x);
            let dy = derivative(t, start.y, control.y, end.y);
    
            const len = Math.hypot(dx, dy);
            if (len < 1e-6) {
                 if (i > 0) {
                    const prevT = ((i-1)/segments) * t_end;
                    dx = derivative(prevT, start.x, control.x, end.x);
                    dy = derivative(prevT, start.y, control.y, end.y);
                } else {
                    dx = control.x - start.x;
                    dy = control.y - start.y;
                }
                if(Math.hypot(dx, dy) < 1e-6) continue;
            }
    
            const normDx = dx / Math.hypot(dx, dy);
            const normDy = dy / Math.hypot(dx, dy);
            
            const nx = -normDy;
            const ny = normDx;
            
            const currentWidth = widthStart + (widthEnd - widthStart) * (t / t_end);
            const halfWidth = Math.max(0, currentWidth) / 2;
            
            points1.push({ x: px + nx * halfWidth, y: py + ny * halfWidth });
            points2.push({ x: px - nx * halfWidth, y: py - ny * halfWidth });
        }
    
        if (points1.length < 2 || points2.length < 2) return "";
    
        const pathParts = [`M ${points1[0].x} ${points1[0].y}`];
        for (let i = 1; i < points1.length; i++) pathParts.push(`L ${points1[i].x} ${points1[i].y}`);
        
        points2.reverse();
        for (let i = 0; i < points2.length; i++) pathParts.push(`L ${points2[i].x} ${points2[i].y}`);
        
        pathParts.push('Z');
        return pathParts.join(' ');
    };

    const renderBrushStroke = (
        annotation: { id: number; segments: BrushPathSegment[] },
        keyPrefix: string
    ) => {
        const { id, segments } = annotation;
        if (!segments) return null;
    
        return (
            <g key={`${keyPrefix}-${id}`}>
                {segments.map((segment, index) => {
                    const { path, settings } = segment;
                    if (path.length < 2) return null;

                    const d = pointsToPath(path.map(mapToScreen));
                    const glowFilterId = `glow-${keyPrefix}-${id}-${index}`;
                    
                    const getStrokeStyle = () => {
                        if (settings.effect === 'rainbow-cycle') return '#ff0000'; // Start color for animation
                        return settings.isNeon ? '#ffffff' : settings.color;
                    };
            
                    const mainStrokeElement = (
                        <path
                            d={d}
                            fill="none"
                            stroke={getStrokeStyle()}
                            strokeWidth={(settings.isNeon ? settings.size / 2 : settings.size) * mapScale}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        >
                            {settings.effect === 'rainbow-cycle' && (
                                <animate 
                                    attributeName="stroke" 
                                    values="#ff0000;#ff7f00;#ffff00;#00ff00;#0000ff;#4b0082;#ee82ee;#ff0000" 
                                    dur="3s" 
                                    repeatCount="indefinite" 
                                />
                            )}
                        </path>
                    );
    
                    const Wrapper = ({ children }: { children: React.ReactNode }) => {
                        if (settings.effect === 'flashing') {
                            return (
                                <g>
                                    <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" />
                                    {children}
                                </g>
                            );
                        }
                         if (settings.effect === 'breathing') {
                            return (
                                <g>
                                    <animate 
                                        attributeName="opacity" 
                                        values="1;0.3;1" 
                                        dur="2.5s" 
                                        repeatCount="indefinite"
                                        calcMode="spline"
                                        keyTimes="0; 0.5; 1"
                                        keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"
                                    />
                                    {children}
                                </g>
                            );
                        }
                        return <>{children}</>;
                    };
    
                    return (
                        <g key={index} opacity={settings.opacity}>
                            <defs>
                                {settings.isNeon && settings.glowSize > 0 && (
                                    <filter id={glowFilterId} x="-100%" y="-100%" width="300%" height="300%">
                                        <feGaussianBlur stdDeviation={settings.glowSize * mapScale} />
                                    </filter>
                                )}
                            </defs>
                            <Wrapper>
                                {settings.isNeon && settings.glowSize > 0 && (
                                    <path d={d} fill="none" stroke={settings.glowColor} strokeWidth={settings.size * mapScale} filter={`url(#${glowFilterId})`} strokeLinecap="round" strokeLinejoin="round" />
                                )}
                                {mainStrokeElement}
                            </Wrapper>
                        </g>
                    );
                })}
            </g>
        );
    };

    const drawingAnnotation = useMemo(() => {
        if (drawingState?.type !== 'brush') return null;
        return {
            id: 0,
            segments: drawingState.segments,
        }
    }, [drawingState]);

    return (
        <svg className="absolute top-0 left-0 w-full h-full pointer-events-none">
            {arePathsVisible && paths.map((p, i) => (
                (p.isVisible !== false && p.path.length > 1) && <g key={`path-${i}`}>{renderPath(p.path, "#fca5a5")}</g>
            ))}

            {/* Render fully committed annotations */}
            {annotations.filter(a => a.isVisible !== false).map(a => {
                if (a.type === AnnotationType.BrushStroke) {
                    return renderBrushStroke(a, `ann`);
                }
                if (a.type === AnnotationType.Arrow) {
                    const screenStart = mapToScreen(a.start);
                    const screenControl = mapToScreen(a.control);
                    const screenEnd = mapToScreen(a.end);
                    
                    const strokeWidthStart = (a.strokeWidthStart ?? 5) * mapScale;
                    const strokeWidthEnd = (a.strokeWidthEnd ?? 1) * mapScale;
                    const arrowheadLength = (a.arrowheadLength ?? 15) * mapScale;
                    const arrowheadWidth = (a.arrowheadWidth ?? 10) * mapScale;
                    const isAnimated = a.isAnimated ?? true;
                    const circleRadius = (a.animatingCircleRadius ?? 4) * mapScale;
                    const circleColor = a.animatingCircleColor ?? a.color;
                    const animationDuration = a.animationDuration ?? 2.5;
                    const isNeon = a.isNeon ?? false;
                    const glowColor = a.glowColor ?? '#00BFFF';
                    const glowSize = (a.glowSize ?? 0) * mapScale;

                    const intersectionT = findIntersectionT(screenStart, screenControl, screenEnd, arrowheadLength);
                    const bodyPathData = getTaperedArrowBodyPath(screenStart, screenControl, screenEnd, strokeWidthStart, strokeWidthEnd, intersectionT);
                    const animationPathData = `M ${screenStart.x} ${screenStart.y} Q ${screenControl.x} ${screenControl.y} ${screenEnd.x} ${screenEnd.y}`;
                    const arrowheadPoints = getArrowheadPoints(screenEnd, screenControl, arrowheadLength, arrowheadWidth);
                    const glowFilterId = `arrow-glow-${a.id}`;

                    return (
                        <g key={a.id}>
                            <defs>
                                {isNeon && glowSize > 0 && (
                                    <filter id={glowFilterId} x="-50%" y="-50%" width="200%" height="200%">
                                        <feGaussianBlur stdDeviation={glowSize} />
                                    </filter>
                                )}
                            </defs>

                            {isNeon && glowSize > 0 && (
                                <g filter={`url(#${glowFilterId})`}>
                                    <path d={bodyPathData} fill={glowColor} />
                                    <polygon points={arrowheadPoints} fill={glowColor} />
                                </g>
                            )}

                            <path d={bodyPathData} fill={a.color} />
                            <polygon points={arrowheadPoints} fill={a.color} />

                            {isAnimated && (
                                <circle r={circleRadius} fill={circleColor} stroke="white" strokeWidth={Math.max(0.5, 1 * mapScale)}>
                                    <animateMotion dur={`${animationDuration}s`} repeatCount="indefinite" path={animationPathData} />
                                </circle>
                            )}
                        </g>
                    );
                }
                if (a.type === AnnotationType.Circle) {
                    const screenCenter = mapToScreen(a.center);
                    const screenRadius = a.radius * mapScale;
                    return (
                        <circle
                            key={a.id}
                            cx={screenCenter.x}
                            cy={screenCenter.y}
                            r={screenRadius}
                            fill={hexToRgba(a.color, 0.3)}
                            stroke={a.color}
                            strokeWidth={Math.max(0.5, 2 * mapScale)}
                        >
                           <animate 
                                attributeName="r"
                                values={`${screenRadius}; ${screenRadius * 0.85}; ${screenRadius}`}
                                dur="1.5s"
                                repeatCount="indefinite"
                                calcMode="spline"
                                keyTimes="0; 0.5; 1"
                                keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"
                            />
                        </circle>
                    );
                }
                return null;
            })}

            {/* Render strokes from the current, uncommitted brush session */}
            {currentBrushAnnotation && renderBrushStroke(currentBrushAnnotation, 'current-session')}
            
            {/* Render the live preview of the stroke currently being drawn */}
            {drawingAnnotation && renderBrushStroke(drawingAnnotation, 'drawing')}
            
            {drawingState && (
                <>
                    {drawingState.type === 'arrow' && drawingState.stage === 'defining-end' && (() => {
                        const screenStart = mapToScreen(drawingState.start);
                        const screenEnd = mapToScreen(drawingState.current);
                        const strokeWidthStart = arrowSettings.strokeWidthStart * mapScale;
                        const arrowheadLength = arrowSettings.arrowheadLength * mapScale;
                        const arrowheadWidth = arrowSettings.arrowheadWidth * mapScale;

                        const arrowheadPoints = getArrowheadPoints(screenEnd, screenStart, arrowheadLength, arrowheadWidth);
                        return (
                            <g>
                                <line x1={screenStart.x} y1={screenStart.y} x2={screenEnd.x} y2={screenEnd.y} stroke={arrowSettings.color} strokeWidth={strokeWidthStart} fill="none" strokeDasharray="5,5" />
                                <polygon points={arrowheadPoints} fill={arrowSettings.color} />
                            </g>
                        );
                    })()}
                    {drawingState.type === 'arrow' && drawingState.stage === 'defining-control' && (() => {
                        const screenStart = mapToScreen(drawingState.start);
                        const screenControl = mapToScreen(drawingState.current);
                        const screenEnd = mapToScreen(drawingState.end);
                        const strokeWidthStart = arrowSettings.strokeWidthStart * mapScale;
                        const strokeWidthEnd = arrowSettings.strokeWidthEnd * mapScale;
                        const arrowheadLength = arrowSettings.arrowheadLength * mapScale;
                        const arrowheadWidth = arrowSettings.arrowheadWidth * mapScale;
                        const isNeon = arrowSettings.isNeon ?? false;
                        const glowColor = arrowSettings.glowColor ?? '#00BFFF';
                        const glowSize = (arrowSettings.glowSize ?? 0) * mapScale;

                        const intersectionT = findIntersectionT(screenStart, screenControl, screenEnd, arrowheadLength);
                        const bodyPathData = getTaperedArrowBodyPath(screenStart, screenControl, screenEnd, strokeWidthStart, strokeWidthEnd, intersectionT);
                        const arrowheadPoints = getArrowheadPoints(screenEnd, screenControl, arrowheadLength, arrowheadWidth);
                        const glowFilterId = 'arrow-drawing-glow-filter';

                        return (
                            <g opacity={0.7}>
                                <defs>
                                    {isNeon && glowSize > 0 && (
                                        <filter id={glowFilterId} x="-50%" y="-50%" width="200%" height="200%">
                                            <feGaussianBlur stdDeviation={glowSize} />
                                        </filter>
                                    )}
                                </defs>
                                {isNeon && glowSize > 0 && (
                                    <g filter={`url(#${glowFilterId})`}>
                                        <path d={bodyPathData} fill={glowColor} />
                                        <polygon points={arrowheadPoints} fill={glowColor} />
                                    </g>
                                )}
                                <path d={bodyPathData} fill={arrowSettings.color} />
                                <polygon points={arrowheadPoints} fill={arrowSettings.color} />
                            </g>
                        );
                    })()}
                    {drawingState.type === 'circle' && <circle cx={mapToScreen(drawingState.start).x} cy={mapToScreen(drawingState.start).y} r={Math.hypot(drawingState.current.x - drawingState.start.x, drawingState.current.y - drawingState.start.y) * mapScale} fill={hexToRgba(selectedColor, 0.2)} stroke={selectedColor} strokeWidth={Math.max(0.5, 2 * mapScale)} strokeDasharray="5,5" />}
                    {drawingState.type === 'path' && (() => {
                        const token = tokens.find(t => t.id === drawingState.pathForTokenId);
                        if (!token) return null;
                        const startPoint = token.path.length > 0 ? token.path[token.path.length - 1] : token.position;
                        const fullSegment = [startPoint, ...drawingState.points];
                        return renderPath(fullSegment, '#fca5a5');
                    })()}
                </>
            )}
        </svg>
    );
};


export const Canvas: React.FC<CanvasProps> = ({ 
    mapImage, mapVideo, mapDimensions, tokens, annotations, currentBrushAnnotation, onDrop, onMouseDown, onMouseMove, onMouseUp, onWheel, 
    drawingState, activeTool, selectedColor, arrowSettings, brushSettings, editingTokenId, onTokenUpdate, onFinishEditing,
    viewTransform, onZoomReset, globalTokenSize, arePathsVisible, mapRotation, isMapRotating,
    videoRef, isVideoPlaying, onVideoPlayPause, videoCurrentTime, onVideoTimeUpdate, videoDuration, onVideoDurationChange, t
}) => {
    const canvasRef = useRef<HTMLDivElement>(null);
    const textInputRef = useRef<HTMLInputElement>(null);
    const [mapRenderInfo, setMapRenderInfo] = useState({ scale: 1, offsetX: 0, offsetY: 0 });

    useEffect(() => {
        const canvasElement = canvasRef.current;
        if (!canvasElement || !mapDimensions) return;
    
        const calculateRenderInfo = () => {
            const { width: canvasWidth, height: canvasHeight } = canvasElement.getBoundingClientRect();
            const { width: mapWidth, height: mapHeight } = mapDimensions;
    
            const mapRatio = mapWidth / mapHeight;
            const canvasRatio = canvasWidth / canvasHeight;
    
            let scale: number, offsetX: number, offsetY: number;
    
            if (mapRatio > canvasRatio) {
                scale = canvasWidth / mapWidth;
                offsetX = 0;
                offsetY = (canvasHeight - mapHeight * scale) / 2;
            } else {
                scale = canvasHeight / mapHeight;
                offsetX = (canvasWidth - mapWidth * scale) / 2;
                offsetY = 0;
            }
            setMapRenderInfo({ scale, offsetX, offsetY });
        };
    
        calculateRenderInfo(); // Initial calculation
    
        const observer = new ResizeObserver(calculateRenderInfo);
        observer.observe(canvasElement);
    
        return () => observer.disconnect();
    }, [mapDimensions]);
    
    const mapToWrapper = useCallback((point: Point): Point => {
        const { scale, offsetX, offsetY } = mapRenderInfo;
        return {
            x: point.x * scale + offsetX,
            y: point.y * scale + offsetY,
        };
    }, [mapRenderInfo]);

    const wrapperToMap = useCallback((point: Point): Point => {
        const { scale, offsetX, offsetY } = mapRenderInfo;
        if (scale === 0) return { x: 0, y: 0 }; // Avoid division by zero
        return {
            x: (point.x - offsetX) / scale,
            y: (point.y - offsetY) / scale,
        };
    }, [mapRenderInfo]);
    
    const screenToMapPoint = useCallback((screenPoint: Point): Point => {
        const canvasEl = canvasRef.current;
        if (!canvasEl) return { x: 0, y: 0 };

        let p = { ...screenPoint };

        // Step 1: Inverse of viewTransform (pan/zoom)
        if (viewTransform) {
            p.x = (p.x - viewTransform.translateX) / viewTransform.scale;
            p.y = (p.y - viewTransform.translateY) / viewTransform.scale;
        }

        // Step 2: Inverse of rotation
        const canvasCenter = { x: canvasEl.clientWidth / 2, y: canvasEl.clientHeight / 2 };
        const angleRad = -mapRotation * (Math.PI / 180); // Negative angle for inverse rotation
        const cos = Math.cos(angleRad);
        const sin = Math.sin(angleRad);
        
        const translatedX = p.x - canvasCenter.x;
        const translatedY = p.y - canvasCenter.y;
        
        p.x = translatedX * cos - translatedY * sin + canvasCenter.x;
        p.y = translatedX * sin + translatedY * cos + canvasCenter.y;

        // Step 3: Inverse of map-fit-to-screen transform
        return wrapperToMap(p);
    }, [viewTransform, wrapperToMap, mapRotation]);

    const getPointInCanvas = (e: React.MouseEvent | React.WheelEvent | React.DragEvent): Point => {
        if (!canvasRef.current) return { x: 0, y: 0 };
        const rect = canvasRef.current.getBoundingClientRect();
        return { x: e.clientX - rect.left, y: e.clientY - rect.top };
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        const data = e.dataTransfer.getData('application/json');
        if (data && mapDimensions) {
            const screenPoint = getPointInCanvas(e);
            const mapPoint = screenToMapPoint(screenPoint);
            onDrop(data, mapPoint);
        }
    };
  
    const handleMouseEvent = (handler: (mapPoint: Point, screenPoint: Point) => void) => (e: React.MouseEvent) => {
        if (!mapDimensions) return;
        const screenPoint = getPointInCanvas(e);
        const mapPoint = screenToMapPoint(screenPoint);
        handler(mapPoint, screenPoint);
    };
    
    const handleMouseUpEvent = () => {
        if (!mapDimensions) return;
        onMouseUp(canvasRef.current?.getBoundingClientRect());
    }

    const handleWheelEvent = (e: React.WheelEvent) => {
        if (!mapDimensions) return;
        const screenPoint = getPointInCanvas(e);
        const mapPoint = screenToMapPoint(screenPoint);
        onWheel(e, mapPoint);
    };

    const getCursor = () => {
        if (drawingState) {
            if (drawingState.type === 'moving' || drawingState.type === 'cloning') return 'cursor-grabbing';
            if (drawingState.type === 'arrow' && drawingState.stage === 'defining-control') return 'cursor-grab';
            if (drawingState.type === 'enlarging') return 'cursor-ns-resize';
        }
        if (activeTool === 'move') return 'cursor-grab';
        if (activeTool === 'clone') return 'cursor-copy';
        if (activeTool === 'zoom') return 'cursor-crosshair';
        if (activeTool === 'enlarge') return 'cursor-ns-resize';
        if (activeTool === 'eraser' || activeTool === 'arrow' || activeTool === 'circle' || activeTool === 'path' || activeTool === 'brush') return 'cursor-crosshair';
        return 'cursor-default';
    }

    const editingToken = tokens.find(t => t.id === editingTokenId);
    
    useEffect(() => {
      if (editingTokenId && textInputRef.current) {
          textInputRef.current.focus();
          textInputRef.current.value = tokens.find(t => t.id === editingTokenId)?.text ?? '';
      }
    }, [editingTokenId, tokens]);

    useEffect(() => {
        const video = videoRef.current;
        if (!video) return;

        if (isVideoPlaying) {
            video.play().catch(e => {
                console.warn("Video autoplay prevented. User must interact with the document first.", e);
                onVideoPlayPause(false);
            });
        } else {
            video.pause();
        }
        video.muted = true;
        video.loop = true;
    }, [isVideoPlaying, mapVideo, onVideoPlayPause, videoRef]);

    const mapPointToFinalScreen = useCallback((mapPoint: Point): Point => {
        let p = mapToWrapper(mapPoint);
        
        const canvasEl = canvasRef.current;
        if (canvasEl && mapRotation !== 0) {
            const canvasCenter = { x: canvasEl.clientWidth / 2, y: canvasEl.clientHeight / 2 };
            const angleRad = mapRotation * (Math.PI / 180);
            const cos = Math.cos(angleRad);
            const sin = Math.sin(angleRad);

            const translatedX = p.x - canvasCenter.x;
            const translatedY = p.y - canvasCenter.y;
            
            p.x = translatedX * cos - translatedY * sin + canvasCenter.x;
            p.y = translatedX * sin + translatedY * cos + canvasCenter.y;
        }

        if (viewTransform) {
            p.x = p.x * viewTransform.scale + viewTransform.translateX;
            p.y = p.y * viewTransform.scale + viewTransform.translateY;
        }

        return p;
    }, [mapToWrapper, viewTransform, mapRotation]);

    const editingTokenScreenPos = editingToken ? mapPointToFinalScreen(editingToken.position) : null;
    const editingTokenScreenSize = editingToken ? (editingToken.size ?? 1) * 16 * mapRenderInfo.scale * (viewTransform?.scale ?? 1) : 16;
    
    const transformParts = [];
    if (viewTransform) {
        transformParts.push(`translate(${viewTransform.translateX}px, ${viewTransform.translateY}px)`);
        transformParts.push(`scale(${viewTransform.scale})`);
    }
    const transformValue = transformParts.length > 0 ? transformParts.join(' ') : 'none';

    return (
        <div
            ref={canvasRef}
            className={`w-full h-full bg-black relative overflow-hidden select-none ${getCursor()}`}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onMouseDown={handleMouseEvent(onMouseDown)}
            onMouseMove={handleMouseEvent(onMouseMove)}
            onMouseUp={handleMouseUpEvent}
            onWheel={handleWheelEvent}
        >
            <div
              className="absolute top-0 left-0 w-full h-full"
              style={{
                transform: transformValue,
                transition: 'transform 0.5s cubic-bezier(0.25, 1, 0.5, 1)',
                transformOrigin: 'top left',
              }}
            >
              <div 
                  className="absolute top-0 left-0 w-full h-full"
                  style={{
                      transform: `rotate(${mapRotation}deg)`,
                      transformOrigin: 'center center',
                      transition: isMapRotating ? 'none' : 'transform 0.3s ease-out',
                  }}
              >
                {mapImage && (
                    <div 
                        className="absolute top-0 left-0 pointer-events-none"
                        style={{
                            width: `${mapRenderInfo.scale * (mapDimensions?.width ?? 0)}px`,
                            height: `${mapRenderInfo.scale * (mapDimensions?.height ?? 0)}px`,
                            left: `${mapRenderInfo.offsetX}px`,
                            top: `${mapRenderInfo.offsetY}px`,
                            backgroundImage: `url(${mapImage})`,
                            backgroundSize: '100% 100%',
                        }}
                    />
                )}
                {mapVideo && (
                    <video
                        ref={videoRef}
                        src={mapVideo}
                        onTimeUpdate={() => videoRef.current && onVideoTimeUpdate(videoRef.current.currentTime)}
                        onLoadedMetadata={() => videoRef.current && onVideoDurationChange(videoRef.current.duration)}
                        onPlay={() => onVideoPlayPause(true)}
                        onPause={() => onVideoPlayPause(false)}
                        playsInline
                        className="absolute top-0 left-0 pointer-events-none"
                        style={{
                            width: `${mapRenderInfo.scale * (mapDimensions?.width ?? 0)}px`,
                            height: `${mapRenderInfo.scale * (mapDimensions?.height ?? 0)}px`,
                            left: `${mapRenderInfo.offsetX}px`,
                            top: `${mapRenderInfo.offsetY}px`,
                            objectFit: 'fill',
                        }}
                    />
                )}
                {!mapImage && !mapVideo && !viewTransform && (
                    <div className="flex items-center justify-center h-full">
                        <p className="text-2xl text-gray-400">{t('canvas_upload_prompt')}</p>
                    </div>
                )}
                
                {mapDimensions && <SvgOverlay tokens={tokens} annotations={annotations} currentBrushAnnotation={currentBrushAnnotation} drawingState={drawingState} paths={tokens.map(t => ({path: t.path, isVisible: t.isVisible}))} selectedColor={selectedColor} mapToScreen={mapToWrapper} mapScale={mapRenderInfo.scale} arrowSettings={arrowSettings} brushSettings={brushSettings} arePathsVisible={arePathsVisible} />}

                {mapDimensions && tokens.filter(t => t.isVisible !== false).map(token => (
                    <Token key={token.id} token={token} mapToScreen={mapToWrapper} mapScale={mapRenderInfo.scale} globalTokenSize={globalTokenSize} viewScale={viewTransform?.scale ?? 1} />
                ))}
                
                {mapDimensions && drawingState?.type === 'cloning' && (
                    <div style={{ opacity: 0.6 }}>
                        <Token
                            key="clone-ghost"
                            token={{ ...drawingState.tokenToClone, position: drawingState.current }}
                            mapToScreen={mapToWrapper}
                            mapScale={mapRenderInfo.scale}
                            globalTokenSize={globalTokenSize}
                            viewScale={viewTransform?.scale ?? 1}
                        />
                    </div>
                )}
              </div>
            </div>
            
            {drawingState?.type === 'zoom' && (() => {
                const { start, current } = drawingState; // These are screen points
                const x = Math.min(start.x, current.x);
                const y = Math.min(start.y, current.y);
                const width = Math.abs(start.x - current.x);
                const height = Math.abs(start.y - current.y);
                return <div className="absolute border-2 border-dashed border-blue-400 bg-blue-400/20 pointer-events-none" style={{ left: x, top: y, width, height }} />;
            })()}

            {viewTransform && (
                <button onClick={onZoomReset} title={t('canvas_reset_zoom')} className="absolute top-4 left-1/2 -translate-x-1/2 bg-gray-800/80 p-2 rounded-full text-white hover:bg-gray-700 transition-colors z-50">
                    <div className="w-6 h-6"><XIcon /></div>
                </button>
            )}

            {mapVideo && (
                <VideoControls
                    isPlaying={isVideoPlaying}
                    onPlayPauseClick={() => onVideoPlayPause(!isVideoPlaying)}
                    currentTime={videoCurrentTime}
                    duration={videoDuration}
                    onSeek={(time) => {
                        if (videoRef.current) {
                            videoRef.current.currentTime = time;
                            onVideoTimeUpdate(time);
                        }
                    }}
                    t={t}
                />
            )}
            
            {editingToken && editingToken.type === UnitType.Text && editingTokenScreenPos && (
                <input
                    ref={textInputRef}
                    type="text"
                    defaultValue={editingToken.text}
                    onBlur={(e) => {
                        onTokenUpdate(editingToken.id, { text: e.target.value });
                        onFinishEditing();
                    }}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            onTokenUpdate(editingToken.id, { text: e.currentTarget.value });
                            onFinishEditing();
                        } else if (e.key === 'Escape') {
                            onFinishEditing();
                        }
                    }}
                    style={{
                        position: 'absolute',
                        left: `${editingTokenScreenPos.x}px`,
                        top: `${editingTokenScreenPos.y}px`,
                        transform: `translate(-50%, -50%) rotate(${editingToken.rotation ?? 0}deg)`,
                        fontSize: `${editingTokenScreenSize}px`,
                        fontFamily: editingToken.fontFamily,
                        color: editingToken.color,
                        background: 'rgba(0,0,0,0.7)',
                        border: `1px solid ${editingToken.color}`,
                        textAlign: 'center',
                        outline: 'none',
                        zIndex: 10,
                        width: `${editingToken.text ? editingToken.text.length * (editingTokenScreenSize * 0.7) + 20 : 100}px` // Auto width
                    }}
                    className="p-1 rounded-md"
                />
            )}
        </div>
    );
};