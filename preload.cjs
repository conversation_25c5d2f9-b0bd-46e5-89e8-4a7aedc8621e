const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Exponer APIs seguras al contexto del renderer
contextBridge.exposeInMainWorld('electronAPI', {
    // Obtener la dirección MAC del sistema
    getMacAddress: () => ipcRenderer.invoke('get-mac-address'),
    
    // Información del sistema
    platform: process.platform,
    
    // Versión de Electron
    versions: {
        node: process.versions.node,
        chrome: process.versions.chrome,
        electron: process.versions.electron
    }
});

// Log para debugging
console.log('Preload script cargado correctamente');

// Manejar errores del preload
window.addEventListener('DOMContentLoaded', () => {
    console.log('DOM cargado, electronAPI disponible:', !!window.electronAPI);
});
